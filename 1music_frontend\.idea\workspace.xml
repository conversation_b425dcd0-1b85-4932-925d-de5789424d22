<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dc423442-82b1-4581-b253-d4d31ef7061d" name="更改" comment="initial">
      <change beforePath="$PROJECT_DIR$/src/app/[locale]/component/audioPlayer.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/[locale]/component/audioPlayer.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;musicdav&quot;
  }
}</component>
  <component name="GithubDefaultAccount">
    <option name="defaultAccountId" value="1566f66f-5318-4d9e-94c1-da2ded0e50d2" />
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/musicdav/1music_frontend.git&quot;,
    &quot;accountId&quot;: &quot;1566f66f-5318-4d9e-94c1-da2ded0e50d2&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="30gQMaXh8v3IY5ELwhtsEl8AkZN" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;js.debugger.nextJs.config.created.client&quot;: &quot;true&quot;,
    &quot;js.debugger.nextJs.config.created.server&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Project/web/1music/1music_frontend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.DirectoryMappings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="npm.Next.js: 服务器端">
    <configuration name="Next.js: 调试客户端" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: 服务器端" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-WS-251.25410.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="dc423442-82b1-4581-b253-d4d31ef7061d" name="更改" comment="" />
      <created>1754046689106</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754046689106</updated>
      <workItem from="1754046690314" duration="704000" />
      <workItem from="1754049897560" duration="440000" />
      <workItem from="1754050352352" duration="258000" />
      <workItem from="1754052349023" duration="75000" />
      <workItem from="1754052436558" duration="181000" />
      <workItem from="1754052652100" duration="103000" />
      <workItem from="1754052774574" duration="506000" />
      <workItem from="1754061827404" duration="186000" />
      <workItem from="1754062046107" duration="25000" />
      <workItem from="1754062464769" duration="565000" />
      <workItem from="1754063263814" duration="531000" />
      <workItem from="1754101880076" duration="415000" />
      <workItem from="1754118468040" duration="110000" />
      <workItem from="1754123592424" duration="265000" />
      <workItem from="1754128938934" duration="1137000" />
      <workItem from="1754130546435" duration="28000" />
      <workItem from="1754131138160" duration="481000" />
      <workItem from="1754133101373" duration="80000" />
      <workItem from="1754221569137" duration="105000" />
      <workItem from="1754222566619" duration="74000" />
      <workItem from="1754223290613" duration="89000" />
      <workItem from="1754224742683" duration="103000" />
      <workItem from="1754225390684" duration="69000" />
      <workItem from="1754226034079" duration="441000" />
      <workItem from="1754230871560" duration="98000" />
      <workItem from="1754276731072" duration="385000" />
      <workItem from="1754277217181" duration="10000" />
      <workItem from="1754277927170" duration="1315000" />
      <workItem from="1754280507808" duration="369000" />
      <workItem from="1754310875871" duration="46000" />
      <workItem from="1754360721687" duration="180000" />
      <workItem from="1754405087206" duration="12000" />
      <workItem from="1754536428001" duration="568000" />
    </task>
    <task id="LOCAL-00001" summary="initial">
      <option name="closed" value="true" />
      <created>1754047195801</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754047195801</updated>
    </task>
    <task id="LOCAL-00002" summary="initial">
      <option name="closed" value="true" />
      <created>1754047382019</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754047382019</updated>
    </task>
    <task id="LOCAL-00003" summary="initial">
      <option name="closed" value="true" />
      <created>1754050121496</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754050121496</updated>
    </task>
    <task id="LOCAL-00004" summary="initial">
      <option name="closed" value="true" />
      <created>1754050287290</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754050287290</updated>
    </task>
    <task id="LOCAL-00005" summary="initial">
      <option name="closed" value="true" />
      <created>1754052665679</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754052665679</updated>
    </task>
    <task id="LOCAL-00006" summary="initial">
      <option name="closed" value="true" />
      <created>1754052799751</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754052799751</updated>
    </task>
    <task id="LOCAL-00007" summary="initial">
      <option name="closed" value="true" />
      <created>1754061984182</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754061984182</updated>
    </task>
    <task id="LOCAL-00008" summary="initial">
      <option name="closed" value="true" />
      <created>1754063274426</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1754063274426</updated>
    </task>
    <task id="LOCAL-00009" summary="initial">
      <option name="closed" value="true" />
      <created>1754101973847</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1754101973847</updated>
    </task>
    <task id="LOCAL-00010" summary="initial">
      <option name="closed" value="true" />
      <created>1754118493497</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1754118493497</updated>
    </task>
    <task id="LOCAL-00011" summary="initial">
      <option name="closed" value="true" />
      <created>1754123672655</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1754123672655</updated>
    </task>
    <task id="LOCAL-00012" summary="initial">
      <option name="closed" value="true" />
      <created>1754129091730</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1754129091730</updated>
    </task>
    <task id="LOCAL-00013" summary="initial">
      <option name="closed" value="true" />
      <created>1754129745152</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1754129745152</updated>
    </task>
    <task id="LOCAL-00014" summary="initial">
      <option name="closed" value="true" />
      <created>1754130563450</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1754130563450</updated>
    </task>
    <task id="LOCAL-00015" summary="initial">
      <option name="closed" value="true" />
      <created>1754131151968</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1754131151968</updated>
    </task>
    <task id="LOCAL-00016" summary="initial">
      <option name="closed" value="true" />
      <created>1754133122743</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1754133122743</updated>
    </task>
    <task id="LOCAL-00017" summary="initial">
      <option name="closed" value="true" />
      <created>1754221643633</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1754221643633</updated>
    </task>
    <task id="LOCAL-00018" summary="initial">
      <option name="closed" value="true" />
      <created>1754222604079</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1754222604079</updated>
    </task>
    <task id="LOCAL-00019" summary="initial">
      <option name="closed" value="true" />
      <created>1754223353745</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1754223353745</updated>
    </task>
    <task id="LOCAL-00020" summary="initial">
      <option name="closed" value="true" />
      <created>1754224821021</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1754224821021</updated>
    </task>
    <task id="LOCAL-00021" summary="initial">
      <option name="closed" value="true" />
      <created>1754225429857</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1754225429858</updated>
    </task>
    <task id="LOCAL-00022" summary="initial">
      <option name="closed" value="true" />
      <created>1754226057965</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1754226057965</updated>
    </task>
    <task id="LOCAL-00023" summary="initial">
      <option name="closed" value="true" />
      <created>1754226291483</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1754226291483</updated>
    </task>
    <task id="LOCAL-00024" summary="initial">
      <option name="closed" value="true" />
      <created>1754226447544</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1754226447544</updated>
    </task>
    <task id="LOCAL-00025" summary="initial">
      <option name="closed" value="true" />
      <created>1754230897389</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1754230897389</updated>
    </task>
    <task id="LOCAL-00026" summary="initial">
      <option name="closed" value="true" />
      <created>1754276750882</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1754276750882</updated>
    </task>
    <task id="LOCAL-00027" summary="initial">
      <option name="closed" value="true" />
      <created>1754276997022</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1754276997022</updated>
    </task>
    <task id="LOCAL-00028" summary="initial">
      <option name="closed" value="true" />
      <created>1754277944052</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1754277944052</updated>
    </task>
    <task id="LOCAL-00029" summary="initial">
      <option name="closed" value="true" />
      <created>1754278233923</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1754278233923</updated>
    </task>
    <task id="LOCAL-00030" summary="initial">
      <option name="closed" value="true" />
      <created>1754278616925</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1754278616925</updated>
    </task>
    <task id="LOCAL-00031" summary="initial">
      <option name="closed" value="true" />
      <created>1754278845881</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1754278845881</updated>
    </task>
    <task id="LOCAL-00032" summary="initial">
      <option name="closed" value="true" />
      <created>1754279171947</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1754279171947</updated>
    </task>
    <task id="LOCAL-00033" summary="initial">
      <option name="closed" value="true" />
      <created>1754280540083</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1754280540083</updated>
    </task>
    <task id="LOCAL-00034" summary="initial">
      <option name="closed" value="true" />
      <created>1754310892928</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1754310892928</updated>
    </task>
    <task id="LOCAL-00035" summary="initial">
      <option name="closed" value="true" />
      <created>1754360843440</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1754360843440</updated>
    </task>
    <task id="LOCAL-00036" summary="initial">
      <option name="closed" value="true" />
      <created>1754536455230</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1754536455230</updated>
    </task>
    <task id="LOCAL-00037" summary="initial">
      <option name="closed" value="true" />
      <created>1754536703075</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1754536703075</updated>
    </task>
    <option name="localTasksCounter" value="38" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$" />
    </ignored-roots>
    <MESSAGE value="initial" />
    <option name="LAST_COMMIT_MESSAGE" value="initial" />
  </component>
</project>