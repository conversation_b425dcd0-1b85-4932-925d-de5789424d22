import React, {useState, useEffect, useRef} from 'react';
import { Card, CardMedia, Box, Skeleton } from '@mui/material';
import {checkDownloadLink, fetchDownloadStatus} from "@/src/app/[locale]/utils";
import {DownloadDialog} from "@/src/app/[locale]/component/musicContainer";


export default function AudioPlayer({coverHeight,songData}) {
    const [audioLoading, setAudioLoading] = useState(false);
    const [coverImageUrl, setCoverImageUrl] = useState('https://picsum.photos/200/200');
    const [imageLoaded, setImageLoaded] = useState(false)
    const [audioSrc, setAudioSrc] = useState(null);
    const [downloadDialogOpen, setDownloadDialogOpen] = useState(false); // 下载对话框状态
    const abortControllerRef = useRef(null);
    let isMounted = useRef(true);

    // 处理专辑封面点击事件
    const handleCoverClick = (e) => {
        e.stopPropagation(); // 防止事件冒泡
        if (songData) {
            setDownloadDialogOpen(true);
        }
    };

    const handlePreview = async () => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        const controller = new AbortController();
        abortControllerRef.current = controller;
        const { signal } = controller;

        setAudioLoading(true);
        let retries = 0;
        const maxRetries = 20;

        const data = {
            title: songData.title,
            album: songData.album,
            artist: songData.artist,
            videoId: songData.videoId,
            request_format: 'webm',
            song_hash: songData.song_hash,
        };

        try {
            while (retries < maxRetries) {
                if (signal.aborted) return;
                const status = await fetchDownloadStatus(data,'preview' ,{ signal });

                if (status.download_url) {
                    let linkReadyRetries = 0;
                    while (linkReadyRetries < maxRetries) {
                        if (signal.aborted) return;
                        const isReady = await checkDownloadLink(status.download_url, { signal });
                        if (isReady) {
                            if (!isMounted.current || signal.aborted) return;
                            setAudioSrc(status.download_url);
                            setCoverImageUrl(songData.thumbnail);
                            setAudioLoading(false);
                            return;
                        }
                        await new Promise((resolve) => setTimeout(resolve, 2000));
                        linkReadyRetries++;
                    }
                    throw new Error('下载链接暂时不可用，请稍后重试。');
                }
                await new Promise((resolve) => setTimeout(resolve, 2000));
                retries++;
            }
            throw new Error('获取下载链接超时，请稍后重试。');
        } catch (err) {
            if (err.name === 'AbortError') {
                console.log('请求被中止。');
            } else {
                console.error('预览失败：', err);
            }
            if (isMounted.current) setAudioLoading(false);
        }
    };

    useEffect(() => {
        if (songData) {
            handlePreview();
        }
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, [songData]);

    useEffect(() => {
        isMounted.current = true;
        return () => {
            isMounted.current = false;
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    useEffect(() => {
        const audioElement = document.getElementById('audio-player');
        if (audioElement) {
            audioElement.load();
            audioElement.play().catch((error) => {
                console.warn('自动播放被阻止:', error);
            });
        }
    }, [audioSrc]);

    return (
        <Card
            sx={{
                display: 'flex',
                alignItems: 'center',
                height: coverHeight,
                width: '100%',
                position: 'fixed',
                bottom: 0,
                left: 0,
                zIndex: 1000,
                borderRadius: 0,
            }}
        >
            <Box
                sx={{
                    height: '100%',
                    width: coverHeight,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
            >
                {(!imageLoaded || (!!audioSrc && audioLoading)) && 
                    <Skeleton variant="rectangular" width={coverHeight} height={coverHeight} />
                }
                <CardMedia
                    component="img"
                    image={coverImageUrl}
                    alt="Album cover"
                    onClick={handleCoverClick}
                    sx={{
                        height: '100%',
                        width: '100%',
                        objectFit: 'cover',
                        margin: 0,
                        cursor: songData ? 'pointer' : 'default', // 有歌曲数据时显示手型光标
                        '&:hover': {
                            opacity: songData ? 0.8 : 1, // 悬停效果
                        },
                    }}
                    onLoad={() => setImageLoaded(true)}
                    onError={() => setImageLoaded(true)}
                />
            </Box>

            <Box
                sx={{
                    flexGrow: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0 16px',
                }}
            >
                <audio loop id="audio-player" controls style={{ width: '100%' }}>
                    <source src={audioSrc} type="audio/webm"/>
                    Your browser does not support the audio element.
                </audio>
            </Box>

            {/* 下载对话框 */}
            {songData && (
                <DownloadDialog
                    open={downloadDialogOpen}
                    onClose={() => setDownloadDialogOpen(false)}
                    song={songData}
                />
            )}
        </Card>
    );
}


