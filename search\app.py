import gevent.monkey
gevent.monkey.patch_all()
import hashlib
import requests
import json
import redis
from flask import Flask, request, jsonify
from flask_cors import CORS
from ytmusicapi import YTMusic,OAuthCredentials

from config import DOWNLOADER_SECRET, REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD, ARTIST_CACHE_EXPIRE

app = Flask(__name__)
CORS(app, supports_credentials=True, origins=["https://1music.cc","https://1music-frontend.vercel.app/"])

# 初始化Redis连接
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        password=REDIS_PASSWORD,
        decode_responses=True
    )
    # 测试连接
    redis_client.ping()
    print("Redis连接成功")
except Exception as e:
    print(f"Redis连接失败: {e}")
    redis_client = None


def transform_thumbnail_url(original_url, new_domain):
    try:
        # Remove protocol if exists
        if '://' in original_url:
            path = original_url.split('://', 1)[1]
        
        # Remove domain part
        path = path.split('/', 1)[1] if '/' in path else ''
        
        # Remove URL parameters if they exist
        path = path.split('?')[0] if '?' in path else path
        
        # Remove trailing size parameters (e.g. w120-h120-l90-rj)
        path_parts = path.split('=')
        if len(path_parts) > 1:
            path = path_parts[0]
            
        # Ensure new_domain doesn't end with slash
        new_domain = new_domain.rstrip('/')
        
        # Join domain with path
        new_url = f"{new_domain}/{path}"
        
        return new_url
        
    except Exception as e:
        return f"Error processing URL: {str(e)}"

def get_client_ip():
    x_forwarded_for = request.headers.get('X-Forwarded-For')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.remote_addr
    return ip

def get_cache_key(browse_id):
    """生成缓存键"""
    return f"artist_songs:{browse_id}"

def get_cached_artist_songs(browse_id):
    """从缓存中获取歌手歌曲数据"""
    if not redis_client:
        return None

    try:
        cache_key = get_cache_key(browse_id)
        cached_data = redis_client.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
    except Exception as e:
        print(f"获取缓存失败: {e}")

    return None

def cache_artist_songs(browse_id, songs_data):
    """缓存歌手歌曲数据"""
    if not redis_client:
        return False

    try:
        cache_key = get_cache_key(browse_id)
        redis_client.setex(
            cache_key,
            ARTIST_CACHE_EXPIRE,
            json.dumps(songs_data, ensure_ascii=False)
        )
        print(f"缓存歌手数据成功: {browse_id}")
        return True
    except Exception as e:
        print(f"缓存数据失败: {e}")
        return False

# 验证 Turnstile 令牌
def verify_turnstile(turnstile_token, turnstile_secret):
    turnstile_url = "https://challenges.cloudflare.com/turnstile/v0/siteverify"
    turnstile_data = {
        "secret": turnstile_secret,
        "response": turnstile_token,
        "remoteip": get_client_ip()
    }
    response = requests.post(turnstile_url, data=turnstile_data)
    turnstile_result = response.json()

    return turnstile_result.get("success", False)

@app.route('/search')
def search_music():
    turnstile_token = request.args.get('token', '')
    turnstile_secret = "0x4AAAAAABBxNbvo4mclJoodKjt8qT1RCeU"  # 请替换为你的实际密钥
    
    if not turnstile_token:
        return jsonify({"message": "缺少验证码令牌"}), 400
    
    if not verify_turnstile(turnstile_token, turnstile_secret):
        return jsonify([]), 200

    yt = YTMusic('oauth.json', oauth_credentials=OAuthCredentials(client_id='262073793575-3stme3bt5pnttoisc650lvb3rn9flu3r.apps.googleusercontent.com', client_secret='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'))

    if 'songs' in request.args:
        search = request.args.get('songs', '')
        playlist_data = yt.search(query=search, filter='songs', limit=30)

    elif 'artists' in request.args:
        search = request.args.get('artists', '')
        res = yt.search(query=search, filter='artists', limit=1)
        if not res:
            return jsonify({"error": "No results found"}), 404

        browse_id = res[0].get('browseId')
        if not browse_id:
            return jsonify({"error": "No browseId found"}), 404

        # 先尝试从缓存获取数据
        cached_songs = get_cached_artist_songs(browse_id)
        if cached_songs:
            print(f"从缓存获取歌手数据: {browse_id}")
            return jsonify(cached_songs)

        # 缓存中没有数据，从API获取
        artist_data = yt.get_artist(browse_id)
        if 'songs' not in artist_data or 'browseId' not in artist_data['songs']:
            return jsonify({"error": "No songs found for artist"}), 404

        song_browse_id = artist_data['songs']['browseId']
        if song_browse_id:
            playlist_data = yt.get_playlist(song_browse_id).get('tracks', [])
        else:
            playlist_data = artist_data['songs']['results']
    else:
        return jsonify({"error": "Invalid query parameter"}), 400

    results = []
    for song in playlist_data:
        thumbnail = next(
            (t['url'] for t in song.get('thumbnails', []) if t['width'] == 120),
            None
        )
        thumbnail = transform_thumbnail_url(thumbnail,'https://pic.1music.cc')
        album_name = (song.get('album') or {}).get('name', '')
        if album_name == '':
            continue

        # 筛选掉时长超过30分钟(1800秒)的歌曲
        duration_seconds = song.get('duration_seconds', 0)
        if duration_seconds > 1800:  # 30分钟 = 30 * 60 = 1800秒
            continue

        song_data = song['title'] + album_name + song['artists'][0]['name'] + song[
            'videoId'] + DOWNLOADER_SECRET
        processed_song = {
            'title': song['title'],
            'album': album_name,
            'artist': song['artists'][0]['name'],
            'thumbnail': thumbnail,
            'videoId': song['videoId'],
            'song_hash': hashlib.sha256(song_data.encode('utf-8')).hexdigest()
        }
        results.append(processed_song)

    # 如果是搜索歌手且有结果，缓存数据
    if 'artists' in request.args and results and 'browse_id' in locals():
        cache_artist_songs(browse_id, results)

    return jsonify(results)


if __name__ == '__main__':
    app.run(port=5000, debug=True)

